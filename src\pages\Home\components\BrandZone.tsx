import ErrorImage from '@/components/ErrorImage';
import useScrollAnimation from '@/hooks/useScrollAnimation';
import { ArrowRightOutlined } from '@ant-design/icons';
import { useGSAP } from '@gsap/react';
import { useModel, useNavigate } from '@umijs/max';
import { Button, Flex, Tabs, TabsProps, Typography } from 'antd';
import { createStyles } from 'antd-style';
import clsx from 'clsx';
import { useRef } from 'react';

const useStyles = createStyles(({ css }) => ({
  brandZoneBg: css`
    height: 446px;
    background: url(${require('@/assets/brandZoneBg.png')}) no-repeat;
    background-position: center 46px;
    background-size: cover;
  `,
  container: css`
    width: 1200px;
    margin: 0 auto;
    overflow: hidden;
  `,
  title: css`
    padding-top: 60px;
    text-align: center;
  `,
  tabContainer: css`
    height: 383px;
    padding: 55px 20px 25px;
  `,
  tabImg: css`
    width: 238px;
    height: 180px;
    display: flex;
    justify-content: center;
    align-items: center;
  `,
  tabContent: css`
    padding-left: 80px;
    flex: 1;
    .title {
      font-size: 24px;
      color: #383838;
    }
  `,
  description: css`
    font-size: 18px;
    color: #808080;
  `,
  moreInfo: css`
    align-self: flex-end;
  `,
}));
const BrandZone = () => {
  const {
    brands = [],
    selectedKey,
    setSelectedKey,
  } = useModel('brands', (model) => ({
    brands: model.brands,
    selectedKey: model.selectedKey,
    setSelectedKey: model.setSelectedKey,
  }));
  const navigate = useNavigate();

  const { styles } = useStyles();
   const isAnimating = useRef(false); 
const wrapperRef = useRef(null); 
  useGSAP(
    (_, context) => {
      // 通过 context.safe() 保证组件卸载时动画被 kill
      const ctx = context?.safe();

      if (isAnimating.current) return;
      isAnimating.current = true;

      // 当前正在显示的面板（新面板）
      const newPane = wrapperRef.current.querySelector(
        `.tab-pane[data-key="${selectedKey}"]`
      );

      // 旧面板（上一个 newPane）
      const oldPane = ctx.prevPane || null;

      /* 1. 旧面板离场动画 */
      const leaveTl = gsap.timeline();
      if (oldPane) {
        leaveTl.to(oldPane, { y: 20, opacity: 0, duration: 0.2 });
      }

      /* 2. 新面板初始状态 */
      gsap.set(newPane, { y: 20, opacity: 0 });

      /* 3. 新面板进入动画 */
      leaveTl.to(
        newPane,
        {
          y: 0,
          opacity: 1,
          duration: 0.25,
          ease: 'power2.out',
          onComplete: () => {
            ctx.prevPane = newPane;        // 更新引用
            isAnimating.current = false;
          }
        },
        oldPane ? '>-0.05' : 0             // 稍微重叠，更顺滑
      );
    },
    /* 依赖数组：activeKey 变化就重新执行 */
    [selectedKey]
  );

  const items: TabsProps['items'] = brands.map((item) => {
    return {
      key: `${item.Id}`,
      label: item.BrandName,
      children: (
        <Flex className={clsx(styles.tabContainer, 'tab-container')} vertical justify="space-between">
          <Flex>
            <div className={styles.tabImg}>
              <ErrorImage src={item.BrandLogoUrl} alt={item.BrandName} />
            </div>
            <Flex vertical className={styles.tabContent}>
              <div>
                <Typography.Title level={4} className="title">
                  {item.BrandName}
                </Typography.Title>
                <Typography.Paragraph className={styles.description}>
                  {item.BrandShortDescription}
                </Typography.Paragraph>
              </div>
            </Flex>
          </Flex>

          <Button
            onClick={() => {
              navigate(`/brand/${item.Id}`);
            }}
            type="primary"
            className={styles.moreInfo}
          >
            了解更多 <ArrowRightOutlined />
          </Button>
        </Flex>
      ),
    };
  });

  return (
    <div id="brandZone">
      <div className={styles.title}>
        <Typography.Title
          level={3}
          style={{ textAlign: 'center', marginBottom: 24 }}
        >
          品牌专区
        </Typography.Title>
      </div>

      <div className={styles.brandZoneBg}>
        <div className={styles.container} ref={container}>
          <Tabs
            activeKey={`${selectedKey}`}
            items={items}
            onChange={(key) => {
              setSelectedKey(Number(key));
            }}
            destroyOnHidden
            animated={false}
          />
        </div>
      </div>
    </div>
  );
};

export default BrandZone;
